{"version": 3, "names": ["_arrayWithHoles", "require", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableRest", "_toArray", "arr", "arrayWithHoles", "iterableToArray", "unsupportedIterableToArray", "nonIterableRest"], "sources": ["../../src/helpers/toArray.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport arrayWithHoles from \"./arrayWithHoles.ts\";\nimport iterableToArray from \"./iterableToArray.ts\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.ts\";\n// @ts-expect-error nonIterableRest is still being converted to TS.\nimport nonIterableRest from \"./nonIterableRest.ts\";\n\nexport default function _toArray<T>(arr: any): T[] {\n  return (\n    arrayWithHoles<T>(arr) ||\n    iterableToArray<T>(arr) ||\n    unsupportedIterableToArray<T>(arr) ||\n    nonIterableRest()\n  );\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,2BAAA,GAAAF,OAAA;AAEA,IAAAG,gBAAA,GAAAH,OAAA;AAEe,SAASI,QAAQA,CAAIC,GAAQ,EAAO;EACjD,OACE,IAAAC,uBAAc,EAAID,GAAG,CAAC,IACtB,IAAAE,wBAAe,EAAIF,GAAG,CAAC,IACvB,IAAAG,mCAA0B,EAAIH,GAAG,CAAC,IAClC,IAAAI,wBAAe,EAAC,CAAC;AAErB", "ignoreList": []}