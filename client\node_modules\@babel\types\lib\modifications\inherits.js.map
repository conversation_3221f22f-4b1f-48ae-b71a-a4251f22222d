{"version": 3, "names": ["_index", "require", "_inheritsComments", "inherits", "child", "parent", "key", "INHERIT_KEYS", "optional", "Object", "keys", "force", "inheritsComments"], "sources": ["../../src/modifications/inherits.ts"], "sourcesContent": ["import { INHERIT_KEYS } from \"../constants/index.ts\";\nimport inheritsComments from \"../comments/inheritsComments.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Inherit all contextual properties from `parent` node to `child` node.\n */\nexport default function inherits<T extends t.Node | null | undefined>(\n  child: T,\n  parent: t.Node | null | undefined,\n): T {\n  if (!child || !parent) return child;\n\n  // optionally inherit specific properties if not null\n  for (const key of INHERIT_KEYS.optional) {\n    // @ts-expect-error Fixme: refine parent types\n    if (child[key] == null) {\n      // @ts-expect-error Fixme: refine parent types\n      child[key] = parent[key];\n    }\n  }\n\n  // force inherit \"private\" properties\n  for (const key of Object.keys(parent)) {\n    if (key[0] === \"_\" && key !== \"__clone\") {\n      // @ts-expect-error Fixme: refine parent types\n      child[key] = parent[key];\n    }\n  }\n\n  // force inherit select properties\n  for (const key of INHERIT_KEYS.force) {\n    // @ts-expect-error Fixme: refine parent types\n    child[key] = parent[key];\n  }\n\n  inheritsComments(child, parent);\n\n  return child;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AAMe,SAASE,QAAQA,CAC9BC,KAAQ,EACRC,MAAiC,EAC9B;EACH,IAAI,CAACD,KAAK,IAAI,CAACC,MAAM,EAAE,OAAOD,KAAK;EAGnC,KAAK,MAAME,GAAG,IAAIC,mBAAY,CAACC,QAAQ,EAAE;IAEvC,IAAIJ,KAAK,CAACE,GAAG,CAAC,IAAI,IAAI,EAAE;MAEtBF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAC1B;EACF;EAGA,KAAK,MAAMA,GAAG,IAAIG,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,EAAE;IACrC,IAAIC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,GAAG,KAAK,SAAS,EAAE;MAEvCF,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAC1B;EACF;EAGA,KAAK,MAAMA,GAAG,IAAIC,mBAAY,CAACI,KAAK,EAAE;IAEpCP,KAAK,CAACE,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;EAC1B;EAEA,IAAAM,yBAAgB,EAACR,KAAK,EAAEC,MAAM,CAAC;EAE/B,OAAOD,KAAK;AACd", "ignoreList": []}