{"version": 3, "names": ["_classApplyDescriptorDestructureSet", "require", "_classPrivateFieldGet", "_classPrivateFieldDestructureSet", "receiver", "privateMap", "descriptor", "classPrivateFieldGet2", "classApplyDescriptorDestructureSet"], "sources": ["../../src/helpers/classPrivateFieldDestructureSet.js"], "sourcesContent": ["/* @minVersion 7.4.4 */\n/* @onlyBabel7 */\n\nimport classApplyDescriptorDestructureSet from \"classApplyDescriptorDestructureSet\";\nimport classPrivateFieldGet2 from \"classPrivateFieldGet2\";\nexport default function _classPrivateFieldDestructureSet(receiver, privateMap) {\n  var descriptor = classPrivateFieldGet2(privateMap, receiver);\n  return classApplyDescriptorDestructureSet(receiver, descriptor);\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,mCAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACe,SAASE,gCAAgCA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAC7E,IAAIC,UAAU,GAAGC,qBAAqB,CAACF,UAAU,EAAED,QAAQ,CAAC;EAC5D,OAAOI,mCAAkC,CAACJ,QAAQ,EAAEE,UAAU,CAAC;AACjE", "ignoreList": []}